<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Storage;


// Models
use App\Models\Berita;


class BeritaController extends Controller
{
    public function index()
    {
        return Inertia::render('landingpage/landing');
    }

    public function berita()
    {
        $berita = Berita::with('user')->latest()->get();
        return Inertia::render('berita',[
            'berita' => $berita
        ]);
    }

    public function addBerita(Request $request)
    {
        // dd($request->all());
        $request->validate([
            'judul' => 'required',
            'deskripsi' => 'required',
            'tanggal' => 'required|date',
            'gambar' => 'required|image|max:2048',
        ]);

        if ($request->hasFile('gambar')) {
            $path = $request->file('gambar')->store('berita', 'public');
        } else {
            return back()->with('error', 'Gagal upload gambar.');
        }
        

        Berita::create([
            'judul_berita' => $request->judul,
            'deskripsi_berita' => $request->deskripsi,
            'tanggal_berita' => $request->tanggal,
            'gambar' => $path,
            'user_id' => auth()->user()->id
        ]);
        return redirect()->back()->with('success', 'Berita berhasil ditambahkan!');

    }

    public function destroy($id)
    {
        // dd(Berita::findOrFail($id));
        $berita = Berita::findOrFail($id);
        if ($berita->gambar && Storage::disk('public')->exists($berita->gambar)) {
            Storage::disk('public')->delete($berita->gambar);
        }

        $berita->delete();

        return redirect()->back()->with('success', 'Berita berhasil dihapus!');
    }

}
