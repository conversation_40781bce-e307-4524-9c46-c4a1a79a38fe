<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Carbon\Carbon;


// Models
use App\Models\Berita;


class BeritaController extends Controller
{
    public function index()
    {
        // Ambil berita terbaru untuk ditampilkan di landing page (limit 6)
        $berita = Berita::with('user')
            ->latest('tanggal_berita')
            ->limit(6)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'judul' => $item->judul_berita,
                    'tanggal' => Carbon::parse($item->tanggal_berita)->format('d M Y'),
                    'gambar' => $item->gambar ? '/storage/' . $item->gambar : 'https://source.unsplash.com/600x400/?news,school',
                    'excerpt' => Str::limit($item->deskripsi_berita, 120),
                    'kategori' => 'Berita',
                    'author' => $item->user->name ?? 'Admin'
                ];
            });

        return Inertia::render('landingpage/landing', [
            'berita' => $berita
        ]);
    }

    public function berita()
    {
        $berita = Berita::with('user')->latest()->get();
        return Inertia::render('berita',[
            'berita' => $berita
        ]);
    }

    public function addBerita(Request $request)
    {
        // dd($request->all());
        $request->validate([
            'judul' => 'required',
            'deskripsi' => 'required',
            'tanggal' => 'required|date',
            'gambar' => 'required|image|max:2048',
        ]);

        if ($request->hasFile('gambar')) {
            $path = $request->file('gambar')->store('berita', 'public');
        } else {
            return back()->with('error', 'Gagal upload gambar.');
        }
        

        Berita::create([
            'judul_berita' => $request->judul,
            'deskripsi_berita' => $request->deskripsi,
            'tanggal_berita' => $request->tanggal,
            'gambar' => $path,
            'user_id' => auth()->user()->id
        ]);
        return redirect()->back()->with('success', 'Berita berhasil ditambahkan!');

    }

    public function destroy($id)
    {
        // dd(Berita::findOrFail($id));
        $berita = Berita::findOrFail($id);
        if ($berita->gambar && Storage::disk('public')->exists($berita->gambar)) {
            Storage::disk('public')->delete($berita->gambar);
        }

        $berita->delete();

        return redirect()->back()->with('success', 'Berita berhasil dihapus!');
    }

}
