<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\BeritaController;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::get('/landing-page', function(){
    return Inertia::render('landingpage/landing');
});

Route::get('/landingPage', [BeritaController::class, 'index'])->name('landing');


Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
    Route::get('data-guru', function() {
        return Inertia::render('data-guru');
    })->name('data-guru');

    // Berita
    Route::get('/berita', [BeritaController::class, 'berita']);
    Route::post('/add/berita', [BeritaController::class, 'addBerita']);
    Route::delete('/delete/berita/{id}', [BeritaController::class, 'destroy']);

});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
